import { Image, Navigator, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import Request from '../../../components/request';
import useRequest from '../../../hooks/use-request';
import { formatCurrency } from '../../../utils';

interface Props {
    // 不需要传入任何props，组件内部自己获取数据
}

export default function EateryManagementCard({}: Props) {
    // 组件内部获取商家数据
    const { result: eatery } = useRequest<Eatery | null>({
        url: 'user/eatery',
    });

    // 组件内部的扫码逻辑
    const scan = () => {
        Taro.scanCode({
            onlyFromCamera: true,
            scanType: ['qrCode'],
            success: (res) => {
                Taro.navigateTo({
                    url: `/pages/coupon/redeem?code=${res.result}`
                });
            },
        });
    };

    // 如果没有商家数据，不渲染组件
    if (!eatery) {
        return null;
    }

    return (
        <View className={'p-[1px] pt-[3px] bg-[#FF724C80] rounded-[12px]'}>
            <View className={'bg-gradient-FF724C rounded-[12px]'}>
                <View className={'p-3 text-white'}>
                    <View className={'text-[17px] flex items-center gap-2 font-semibold'}>
                        <Image className={'size-5'} src={require('../../../assets/eatery.svg')} />
                        {eatery.name}
                        <Image
                            className={'size-5 ms-auto'}
                            src={require('../../../assets/edit.svg')}
                            onClick={() => {
                                Taro.navigateTo({
                                    url: '/pages/eatery/create?from=user'
                                });
                            }}
                        />
                    </View>
                    {eatery.status === 1 && (
                        <View className={'mt-4 flex flex-col gap-2'}>
                            <View className={'text-[13px]'}>已核销金额(本月)</View>
                            <View className={'flex items-baseline'}>
                                <Text className={'text-[18px]'}>￥</Text>
                                <Text className={'text-[32px]'}>
                                    <Request url={'eatery/coupon/amount'}>
                                        {(data) => {
                                            return formatCurrency(data.amount);
                                        }}
                                    </Request>
                                </Text>
                                <Navigator url={'/pages/eatery/coupon'} className={'flex items-center'}>
                                    <Text className={'text-[13px] ms-3'}>查看明细</Text>
                                    <Image src={require('../../../assets/right-white.svg')} className={'size-[16px]'} />
                                </Navigator>
                            </View>
                        </View>
                    )}
                </View>
                {eatery.status === 0 && (
                    <View className={'rounded-[12px] py-6 px-4 bg-white flex flex-col justify-center items-center gap-1'}>
                        <Image className={'size-[64px]'} src={require('../../../assets/review.png')} />
                        <View className={'text-[15px]'}>店铺信息审核中...</View>
                    </View>
                )}
                {eatery.status === 2 && (
                    <View className={'rounded-[12px] py-6 px-4 bg-white flex flex-col justify-center items-center gap-1'}>
                        <Image className={'size-[64px]'} src={require('../../../assets/review.png')} />
                        <View className={'text-[15px]'}>店铺信息审核未通过</View>
                    </View>
                )}
                {eatery.status === 1 && (
                    <View className={'rounded-[12px] py-3 px-4 bg-white flex items-center justify-around gap-1'}>
                        <Navigator url={'/pages/food/index'} className={'flex flex-col items-center gap-2'}>
                            <Image src={require('../../../assets/bowl.svg')} className={'size-[20px]'} />
                            <View className={'text-[15px]'}>商品管理</View>
                        </Navigator>
                        {eatery.pivot?.access_level === 60 && (
                            <>
                                <View className={'w-[1px] h-[20px] bg-black opacity-10'} />
                                <Navigator url={'/pages/eatery/member'} className={'flex flex-col items-center gap-2'}>
                                    <Image src={require('../../../assets/peoples.svg')} className={'size-[20px]'} />
                                    <View className={'text-[15px]'}>员工管理</View>
                                </Navigator>
                            </>
                        )}
                        <View className={'w-[1px] h-[20px] bg-black opacity-10'} />
                        <View onClick={scan} className={'flex flex-col items-center gap-2'}>
                            <Image src={require('../../../assets/scan.svg')} className={'size-[20px]'} />
                            <View className={'text-[15px]'}>扫码核销</View>
                        </View>
                        {!!eatery.can_reserve && (
                            <>
                                <View className={'w-[1px] h-[20px] bg-black opacity-10'} />
                                <Navigator url={'/pages/reserve/index'} className={'flex flex-col items-center gap-2'}>
                                    <Image src={require('../../../assets/calendar.svg')} className={'size-[20px]'} />
                                    <View className={'text-[15px]'}>预定管理</View>
                                </Navigator>
                            </>
                        )}
                    </View>
                )}
            </View>
        </View>
    );
}
